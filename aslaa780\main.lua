-- main.lua - Air780EG Enhanced Firmware
-- <PERSON>a<PERSON><PERSON><PERSON> needs PROJECT and VERSION information
PROJECT = "mqtt_sensor_demo"
-- IoT platform requires xxx.yyy.zzz format version numbers
-- Note: Only xxx and zzz are used, yyy is ignored but must be present
-- Recommend keeping middle digit as 000
VERSION = "002.000.000"
PRODUCT_KEY = "AtqpMY7HfMCjxCgLw200LlNN2ZsiVszc"

-- Load core modules
local sys = require("sys")
local PinModule = require("pin_module")
local vars = require("variables")
local my_utils = require("my_utils")
local commands = require("commands")

-- Load optional modules
local update_available, update = pcall(require, "update")
if not update_available then
    local ota_available, ota_update = pcall(require, "ota_update")
    if ota_available then
        update = ota_update
        update_available = true
    else
        update = {request = function() return false end}
        update_available = false
    end
end

local mqtt_module_available, mqtt_module = pcall(require, "mqtt_module")
if not mqtt_module_available then
    mqtt_module = {
        init = function() return false end,
        publish = function() return false end,
        is_ready = function() return false end,
        get_client_id = function() return "unknown" end
    }
end

-- Configuration
local PRINT_INTERVAL = 5000
local mqtt_connection_beep_played = false
local ota_in_progress = false
local last_voltage = 0

-- Time tracking
sys.timerLoopStart(function() vars.currentTime = vars.currentTime + 1 end, 1000)

-- Sensor configs
local SHTC3 = {i2c_id = 0, addr = 0x70, cmd_wakeup = 0x3517, cmd_sleep = 0xB098,
               cmd_measure_normal = 0x7866, temp_offset = 0}
local GPS = require("libgnss_gps")
local ADC_CONFIG = {id = 1, channel = 0, scaling_factor = 16.14}

-- Simple logging
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO]", ...) end,
        warn = function(tag, ...) print("[WARN]", ...) end,
        error = function(tag, ...) print("[ERROR]", ...) end
    }
end

-- Simple JSON encoder
if not json then
    _G.json = {
        encode = function(obj)
            if type(obj) ~= "table" then return tostring(obj) end
            local res = "{"
            for k, v in pairs(obj) do
                res = res .. '"' .. k .. '":' .. (type(v) == "string" and '"' .. v .. '"' or tostring(v)) .. ","
            end
            return res:sub(1, -2) .. "}"
        end
    }
end

-- Initialize SHTC3 sensor
local function initSHTC3()
    local setup_result = i2c.setup(SHTC3.i2c_id, 100000)
    if setup_result ~= 1 then return false end

    local result = i2c.send(SHTC3.i2c_id, SHTC3.addr, {0x00})
    if not result then return false end

    print("SHTC3 sensor initialized")
    return true
end

-- Sensor functions
local function readSHTC3()
    local wakeup_cmd_high = (SHTC3.cmd_wakeup >> 8) & 0xFF
    local wakeup_cmd_low = SHTC3.cmd_wakeup & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {wakeup_cmd_high, wakeup_cmd_low})
    sys.wait(1)

    local measure_cmd_high = (SHTC3.cmd_measure_normal >> 8) & 0xFF
    local measure_cmd_low = SHTC3.cmd_measure_normal & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {measure_cmd_high, measure_cmd_low})
    sys.wait(15)

    local data = i2c.recv(SHTC3.i2c_id, SHTC3.addr, 6)
    if not data or #data ~= 6 then return {temperature = 25.0, humidity = 50.0} end

    local temp_raw = (data:byte(1) << 8) | data:byte(2)
    local humidity_raw = (data:byte(4) << 8) | data:byte(5)
    local temperature = -45.0 + 175.0 * (temp_raw / 65535.0) + SHTC3.temp_offset
    local humidity = 100.0 * (humidity_raw / 65535.0)

    local sleep_cmd_high = (SHTC3.cmd_sleep >> 8) & 0xFF
    local sleep_cmd_low = SHTC3.cmd_sleep & 0xFF
    i2c.send(SHTC3.i2c_id, SHTC3.addr, {sleep_cmd_high, sleep_cmd_low})

    return {temperature = temperature, humidity = humidity}
end

local function initGPS()
    if not GPS then return false end
    local success = GPS.init()
    if success then print("GPS initialized") end
    return success
end

local function getGPSPosition()
    return GPS and GPS.getPosition() or nil
end

local function initADC()
    if adc and adc.open then
        local result = adc.open(ADC_CONFIG.id, ADC_CONFIG.channel)
        if result == true or result == 1 then
            print("ADC initialized")
            return true
        end
    end
    print("ADC not available, using simulated voltage")
    return false
end

local function readVoltage()
    if adc and adc.read then
        local adcval, voltage_mv = adc.read(ADC_CONFIG.id, ADC_CONFIG.channel)
        if voltage_mv then
            return (voltage_mv * ADC_CONFIG.scaling_factor) / 1000.0 + (vars.voltage_offset or 0)
        end
    end
    -- Fallback: simulate voltage reading
    return 12.4 + (vars.voltage_offset or 0)
end

local function getRSSI()
    if mobile and mobile.signal then
        return mobile.signal() or 0
    elseif mobile and mobile.csq then
        return mobile.csq() or 0
    end
    return 0
end

-- Format sensor data as JSON
local function formatSensorData(temp, humidity, voltage, gps, rssi)
    local lat_str, lon_str = "0 N", "0 E"
    if gps and gps.latitude and gps.longitude then
        local lat = tonumber(gps.latitude) or 0
        local lon = tonumber(gps.longitude) or 0

        if lat ~= 0 and lon ~= 0 then
            local lat_deg = math.floor(lat / 100)
            local lat_min = lat - (lat_deg * 100)
            lat_str = string.format("%.5f N", lat_deg + (lat_min / 60))

            local lon_deg = math.floor(lon / 100)
            local lon_min = lon - (lon_deg * 100)
            lon_str = string.format("%.5f E", lon_deg + (lon_min / 60))
        end
    end

    -- Ensure all values are properly formatted and validated
    local safe_humidity = math.floor((tonumber(humidity) or 29) + 0.5)
    local safe_rssi = math.floor(tonumber(rssi) or 0)
    local safe_voltage = tonumber(voltage) or 10.0
    local safe_speed = math.floor(tonumber((gps and gps.speed) or 0) or 0)
    local safe_temp = math.floor((tonumber(temp) or 30) + 0.5)
    local safe_version = tostring(VERSION or "002.000.000")

    -- Ensure all string values are safe
    local safe_lon = tostring(lon_str or "0 E")
    local safe_lat = tostring(lat_str or "0 N")

    return string.format('{"Lon":"%s","Lat":"%s","hum":%d,"ver":"%s","rssi":%d,"volt":%.3f,"Speed":%d,"temp":%d,"motion":0,"light":0}',
        safe_lon, safe_lat, safe_humidity, safe_version, safe_rssi, safe_voltage, safe_speed, safe_temp)
end

-- Consolidated command functions
local function playBeep(pattern)
    if vars.sound_flag then
        sys.taskInit(function()
            if pattern == "check" then PinModule.beepPattern(1, 300, 0, 2000)
            elseif pattern == "lock" then PinModule.beepPattern(2, 200, 200, 2500)
            elseif pattern == "unlock" then PinModule.beepPattern(3, 150, 150, 3000)
            elseif pattern == "as" then PinModule.beepPattern(1, 500, 0, 1800)
            else PinModule.beepPattern(1, 200, 0, 2000) end
        end)
    end
end

-- Command functions are now handled by the commands module
-- Keep local aliases for backward compatibility
local function lockCommand()
    commands.lockCommand()
end

local function unlockCommand()
    commands.unlockCommand()
end

local function asCommand()
    commands.asCommand()
end

local function untarCommand()
    commands.untarCommand()
end

-- Simplified SMS functions
local function sendSms(phoneNumber, message)
    if sms then
        -- Remove + prefix if present, as some SMS libraries handle international format differently
        if string.match(phoneNumber, "^%+") then
            phoneNumber = string.sub(phoneNumber, 2)  -- Remove the + prefix
        end
        return sms.send(phoneNumber, message)
    end
    return false
end

local function smsCallback(num, data, datetime)
    local original_num = num  -- Store the original full number for sending replies
    local short_num = string.sub(num, -8)  -- Extract last 8 digits for comparison

    -- Forward special keywords to MQTT
    local keywords = {"Tand", "TG", "hugatsaa", "kod", "code", "opt"}
    for _, keyword in ipairs(keywords) do
        if string.find(data, keyword) then
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"sms":"' .. data .. '"}')
            end
            return
        end
    end

    -- Handle restart command
    if string.lower(data) == "restart" then
        sendSms(original_num, "System restarting...")
        sys.taskInit(function()
            sys.wait(2000)
            if rtos and rtos.reboot then rtos.reboot() end
        end)
        return
    end

    -- Handle Air720-style phone number configuration
    for i = 1, 3 do
        local pattern = "x123456x" .. i
        if string.find(data, pattern) then
            local start_pos = string.find(data, pattern) + string.len(pattern)
            local end_pos = string.find(data, "xx") - 1
            if start_pos and end_pos and end_pos >= start_pos then
                local phone_num = string.sub(string.sub(data, start_pos, end_pos), -8)
                if string.len(phone_num) == 8 and my_utils.writeToFile("/user_dir/phone" .. i .. ".txt", phone_num) then
                    vars["phone_number" .. i] = phone_num
                    sendSms(original_num, "success")
                else
                    sendSms(original_num, "fail")
                end
            else
                sendSms(original_num, "fail")
            end
            return
        end
    end

    -- Check authorization (compare using short numbers)
    if short_num ~= vars.phone_number1 and short_num ~= vars.phone_number2 and short_num ~= vars.phone_number3 then
        if string.len(short_num) >= 8 then
            sendSms(original_num, "taniulaagvi dugaar!")  -- Send to original full number
        end
        return
    end

    -- Store for processing (use original number for replies)
    vars.sms_data = data
    vars.callback_number = original_num
end

-- Initialize SMS
local function initSMS()
    if sms then
        sms.setNewSmsCb(smsCallback)
        print("SMS initialized")
        return true
    end
    return false
end

-- Task to read and print sensor data
local function sensorTask()
    print("Starting sensor reading task")

    -- Wait for system to stabilize
    sys.wait(2000)

    while true do
        -- Read sensor data
        local temp_hum = readSHTC3()
        local temperature = temp_hum and temp_hum.temperature or 25.0
        local humidity = temp_hum and temp_hum.humidity or 50.0
        local voltage = readVoltage() or 12.0
        local gps = getGPSPosition()
        local rssi = getRSSI() or 0

        -- Format data as JSON
        local json_data = formatSensorData(temperature, humidity, voltage, gps, rssi)

        -- Print sensor data in compact format
        print(json_data)
        print("T:" .. string.format("%.1f", temperature) .. "°C H:" .. string.format("%.1f", humidity) .. "% V:" .. string.format("%.2f", voltage) .. "V RSSI:" .. rssi .. " GPS:" .. (gps and "OK" or "NO"))



        -- Wait for the next reading interval
        sys.wait(PRINT_INTERVAL)
    end
end

-- Initialize mobile network
local function initMobile()
    print("\n=== Mobile Network Initialization Start ===")

    -- Check if mobile module is available
    if not mobile then
        print("Mobile module not available")
        return false
    end

    -- Wait for network registration
    print("Waiting for network registration...")
    local timeout = 30  -- 30 seconds timeout
    local start_time = os.time()

    while os.time() - start_time < timeout do
        -- Check network registration status
        if mobile.status then
            local status = mobile.status()
            print("Network status: " .. tostring(status))

            -- Different versions of LuatOS may return different status formats
            -- Some return strings like "REGISTERED", others return numbers like 1 or 2
            if status == "REGISTERED" or status == "REGISTERED_ROAMING" or
               status == 1 or status == 2 or status == 5 then
                print("Network registered successfully")

                -- Get signal strength
                local rssi = getRSSI()
                print("Signal strength (RSSI): " .. rssi .. " dBm")

                -- Get operator info if available
                if mobile.getOperator then
                    local operator = mobile.getOperator()
                    print("Network operator: " .. (operator or "Unknown"))
                end

                print("Mobile network initialized successfully")
                -- Turn on NetLED when network is registered
                PinModule.setNetLED(1)
                print("=== Mobile Network Initialization Complete ===\n")
                return true
            end
        end

        sys.wait(1000)  -- Check every second
    end

    -- Even if registration times out, we can still try to get signal strength
    local rssi = getRSSI()
    if rssi and rssi ~= 0 then
        print("Network registration timed out, but signal detected (RSSI: " .. rssi .. " dBm)")
        print("Mobile network partially initialized")
        -- Turn on NetLED when signal is detected even without full registration
        PinModule.setNetLED(1)
        print("=== Mobile Network Initialization Complete with Warnings ===\n")
        return true
    end

    print("Warning: Network registration timeout")
    print("=== Mobile Network Initialization Complete with Warnings ===\n")
    return false
end

-- Function to publish sensor data to MQTT
-- This function starts a task to publish data and returns immediately
local function publishSensorData()
    -- Start a task to handle the publishing (allows yielding)
    sys.taskInit(function()
        -- Check if MQTT is connected
        if mqtt_module.is_ready() then
            -- Read sensor data
            local temp_hum = readSHTC3()
            local temperature = temp_hum and temp_hum.temperature or 25.0
            local humidity = temp_hum and temp_hum.humidity or 50.0
            local voltage = readVoltage() or 12.0
            local gps = getGPSPosition()
            local rssi = getRSSI() or 0

            -- Format and publish sensor data
            local json_data = formatSensorData(temperature, humidity, voltage, gps, rssi)
            local publish_result = mqtt_module.publish(json_data)

            if publish_result then
                print("Published sensor data to MQTT")
                -- Publish a success event that other parts of the code can listen for
                sys.publish("MQTT_PUBLISH_SUCCESS")
            else
                print("Failed to publish sensor data to MQTT")
                -- Publish a failure event that other parts of the code can listen for
                sys.publish("MQTT_PUBLISH_FAILURE")
            end
        else
            print("MQTT not connected, skipping publish")
            -- Publish a failure event that other parts of the code can listen for
            sys.publish("MQTT_PUBLISH_FAILURE")
        end
    end)

    -- Return true to indicate that the task was started successfully
    return true
end

-- Simplified UART task
local function uartTask()
    local lastTime = {lock = 0, unlock = 0, mirror = 0}
    local uart_id = 1

    if not uart then return end

    if uart.exist and not uart.exist(1) then uart_id = 0 end
    if gpio then pcall(gpio.setup, 18, nil, gpio.PULLUP) end

    local setup_result = uart.setup(uart_id, 9600, 8, 1, uart.NONE, uart.LSB, 1024)
    if setup_result ~= 0 then return end

    if uart.on then
        uart.on(uart_id, "receive", function(id, len)
            local data = uart.read(id, len)
            if data and #data > 8 then
                local cmd_id = data:sub(9, 9)
                local now = vars.currentTime
                if cmd_id == "8" and now - lastTime.lock > 2 then
                    lockCommand(); lastTime.lock = now
                elseif cmd_id == "4" and now - lastTime.unlock > 2 then
                    unlockCommand(); lastTime.unlock = now
                elseif cmd_id == "2" and now - lastTime.mirror > 2 then
                    lastTime.mirror = now
                end
            end
        end)

        while true do
            sys.wait(30000)  -- Status check every 30 seconds
        end
    end
end

-- Simplified voltage monitoring (sensor data only)
local function monitorVoltage()
    local new_voltage = tonumber(readVoltage()) or 0
    if new_voltage == 0 then return end

    local voltage_diff = math.abs(new_voltage - last_voltage)
    if last_voltage > 0 and voltage_diff >= (vars.voltage_threshold or 0.5) and vars.voltage_notify_flag then
        sys.taskInit(function()
            if mqtt_module.is_ready() then
                publishSensorData()
            end
        end)
    end
    last_voltage = new_voltage
end

-- Simplified OTA functions
local function update_cb(result)
    ota_in_progress = false
    print("OTA update " .. (result and "success" or "failed"))
    if result and rtos and rtos.reboot then
        sys.taskInit(function() sys.wait(2000); rtos.reboot() end)
    end
end

local function startOTAUpdate()
    if ota_in_progress or not update_available then return false end
    print("Starting OTA update...")
    ota_in_progress = true
    sys.taskInit(function()
        sys.waitUntil("IP_READY", 30000)
        update.request(update_cb)
    end)
    return true
end

-- Simplified MQTT message handler
local function handleMQTTMessage(topic, payload)
    local success, data = pcall(json.decode, payload)
    if success and data and data.command then
        playBeep("check")

        if data.command == "check" then
            publishSensorData()
        elseif data.command == "lock" then
            lockCommand(); publishSensorData()
        elseif data.command == "unlock" then
            unlockCommand(); publishSensorData()
        elseif data.command == "as" then
            if vars.isLicensed then asCommand() end
            publishSensorData()
        elseif data.command == "untar" then
            untarCommand(); publishSensorData()
        elseif data.command == "update" then
            startOTAUpdate()
        elseif data.command == "restart" then
            print("Restart command received via MQTT")
            sys.taskInit(function()
                sys.wait(2000)
                if rtos and rtos.reboot then
                    rtos.reboot()
                end
            end)
        elseif data.command == "ota_debug" then
            -- Debug OTA configuration
            print("=== OTA Debug Information ===")
            print("PROJECT:", PROJECT)
            print("VERSION:", VERSION)
            print("PRODUCT_KEY:", PRODUCT_KEY)
            if mobile and mobile.imei then
                print("IMEI:", mobile.imei())
            end
            print("Available modules:")
            print("- rtos:", rtos and "available" or "not available")
            print("- mobile:", mobile and "available" or "not available")
            print("- libfota:", libfota and "available" or "not available")
            if update and update.test then
                print("Running OTA module test...")
                update.test()
            end
            print("=== End OTA Debug ===")
        elseif data.command == "uart_test" then
            -- UART testing command via MQTT (receive-only mode)
            print("=== MQTT UART TEST COMMAND (LuatOS API) ===")
            print("Note: UART is configured for receive-only on Pin 17 (GPIO18/MAIN_RXD)")

            if uart then
                -- Check UART status using official API
                local uart_id = 1
                if uart.exist and not uart.exist(1) then
                    uart_id = 0
                end

                print("Using UART" .. uart_id .. " for testing")

                if uart.rxSize then
                    local rx_size = uart.rxSize(uart_id)
                    print("Current UART RX buffer: " .. tostring(rx_size) .. " bytes")

                    if rx_size and rx_size > 0 and uart.read then
                        local buffered_data = uart.read(uart_id, rx_size)
                        if buffered_data then
                            print("Buffered data: [" .. buffered_data .. "]")
                            print("Data length: " .. #buffered_data .. " bytes")
                            print("Data (hex): " .. string.gsub(buffered_data, ".", function(c)
                                return string.format("%02X ", string.byte(c))
                            end))
                        end
                    else
                        print("No data in RX buffer")
                    end
                else
                    print("❌ uart.rxSize function not available")
                end

                -- Test sending data (for loopback testing)
                local test_message = data.message or "Test from MQTT"
                if uart.write then
                    print("Sending test message: [" .. test_message .. "]")
                    local sent = uart.write(uart_id, test_message)
                    print("Bytes sent: " .. tostring(sent))
                else
                    print("❌ uart.write function not available")
                end
            else
                print("❌ UART module not available")
            end
        elseif data.command == "uart_status" then
            -- UART status command via MQTT using LuatOS API
            print("=== UART STATUS REQUEST (LuatOS API) ===")
            print("UART module available: " .. tostring(uart ~= nil))

            if uart then
                print("UART functions available:")
                print("  uart.setup: " .. tostring(uart.setup ~= nil))
                print("  uart.read: " .. tostring(uart.read ~= nil))
                print("  uart.write: " .. tostring(uart.write ~= nil))
                print("  uart.rxSize: " .. tostring(uart.rxSize ~= nil))
                print("  uart.on: " .. tostring(uart.on ~= nil))
                print("  uart.exist: " .. tostring(uart.exist ~= nil))

                -- Check which UART ports exist
                if uart.exist then
                    for i = 0, 2 do
                        local exists = uart.exist(i)
                        print("UART" .. i .. " exists: " .. tostring(exists))

                        if exists and uart.rxSize then
                            local rx_size = uart.rxSize(i)
                            print("UART" .. i .. " RX buffer: " .. tostring(rx_size) .. " bytes")
                        end
                    end
                else
                    print("uart.exist function not available")
                end
            end

            -- Check GPIO status (Pin 17 = GPIO18 = MAIN_RXD)
            if gpio then
                print("GPIO module available: true")
                print("Pin 17 (GPIO18/MAIN_RXD) state: " .. tostring(gpio.get(18)))
            else
                print("GPIO module not available")
            end
        elseif data.command == "uart_pin_test" then
            -- Test UART pin configuration (receive-only)
            print("=== UART PIN TEST (Pin 17 / GPIO18 / MAIN_RXD) ===")

            if gpio then
                -- Test GPIO18 (Pin 17 - MAIN_RXD)
                print("Testing Pin 17 (GPIO18/MAIN_RXD)...")

                -- Read current pin state
                local gpio18_state = gpio.get(18)
                print("Pin 17 (GPIO18/MAIN_RXD) current state: " .. tostring(gpio18_state))

                -- Test pin stability over time
                print("Testing Pin 17 (GPIO18) stability...")
                for i = 1, 5 do
                    local state = gpio.get(18)
                    print("Pin 17 (GPIO18) reading " .. i .. ": " .. tostring(state))
                    sys.wait(100)
                end

                -- Reconfigure GPIO18 (Pin 17) for UART RX
                gpio.setup(18, nil, gpio.PULLUP)
                print("Pin 17 (GPIO18/MAIN_RXD) reconfigured with pullup")
                print("Note: Pin 17 has hardware pull-up to VDD_EXT and wake-up capability")
            else
                print("GPIO module not available for pin testing")
            end
        elseif data.command == "geely_atlas_on" then
            -- Enable Geely Atlas mode
            commands.enableGeelyMode()
            print("Geely Atlas mode enabled via MQTT")
        elseif data.command == "geely_atlas_off" then
            -- Disable Geely Atlas mode
            commands.disableGeelyMode()
            print("Geely Atlas mode disabled via MQTT")
        elseif data.command == "geely_status" then
            -- Get Geely Atlas mode status
            local status = commands.getGeelyModeStatus()
            print("Geely Atlas mode status:", status and "enabled" or "disabled")
            if mqtt_module.is_ready() then
                mqtt_module.publish(string.format('{"geely_atlas_mode":%s}', status and "true" or "false"))
            end
        elseif data.command == "set_timing" then
            -- Set timing parameter: {"command":"set_timing","parameter":"lock_press","value":1500}
            local param = data.parameter
            local value = tonumber(data.value)
            if param and value and value > 0 then
                local success = commands.setTimingParameter(param, value)
                if success then
                    print(string.format("Timing parameter %s set to %d ms", param, value))
                else
                    print("Failed to set timing parameter:", param)
                end
            else
                print("Invalid timing parameter or value")
            end
        elseif data.command == "get_timing" then
            -- Get all timing parameters
            local params = commands.getTimingParameters()
            print("Current timing parameters:")
            for k, v in pairs(params) do
                print("  " .. k .. ": " .. v .. "ms")
            end
            if mqtt_module.is_ready() then
                local json_params = json.encode(params)
                mqtt_module.publish('{"timing_parameters":' .. json_params .. '}')
            end

        elseif data.command == "relay1" then
            -- Test Relay1 (GPIO 29)
            local state = tonumber(data.state)
            if state == 0 or state == 1 then
                PinModule.relayControl("Relay1", state)
                print("Relay1 (GPIO 29) set to " .. state)
            else
                print("Invalid Relay1 state. Use 0 (off) or 1 (on)")
            end
        elseif data.command == "relay2" then
            -- Test Relay2 (GPIO 30)
            local state = tonumber(data.state)
            if state == 0 or state == 1 then
                PinModule.relayControl("Relay2", state)
                print("Relay2 (GPIO 30) set to " .. state)
            else
                print("Invalid Relay2 state. Use 0 (off) or 1 (on)")
            end
        elseif data.command == "relay3" then
            -- Test Relay3 (GPIO 31)
            local state = tonumber(data.state)
            if state == 0 or state == 1 then
                PinModule.relayControl("Relay3", state)
                print("Relay3 (GPIO 31) set to " .. state)
            else
                print("Invalid Relay3 state. Use 0 (off) or 1 (on)")
            end
        elseif data.command == "keypower" then
            -- Test KeyPower (GPIO 10)
            local state = tonumber(data.state)
            if state == 0 or state == 1 then
                PinModule.relayControl("KeyPower", state)
                print("KeyPower (GPIO 10) set to " .. state)
            else
                print("Invalid KeyPower state. Use 0 (off) or 1 (on)")
            end
        elseif data.command == "key1" then
            -- Test Key1 (GPIO 8)
            local state = tonumber(data.state)
            if state == 0 or state == 1 then
                PinModule.relayControl("Key1", state)
                print("Key1 (GPIO 8) set to " .. state)
            else
                print("Invalid Key1 state. Use 0 (off) or 1 (on)")
            end
        elseif data.command == "key2" then
            -- Test Key2 (GPIO 11)
            local state = tonumber(data.state)
            if state == 0 or state == 1 then
                PinModule.relayControl("Key2", state)
                print("Key2 (GPIO 11) set to " .. state)
            else
                print("Invalid Key2 state. Use 0 (off) or 1 (on)")
            end
        elseif data.command == "beeptest" then
            -- Test Beep (GPIO 17) - uses the existing playBeep function
            local beep_type = data.type or "check"
            print("Testing Beep (GPIO 17) with type: " .. beep_type)
            playBeep(beep_type)
        elseif data.command == "notify_on" then
            -- Enable voltage notifications (Air720 compatible JSON response)
            vars.voltage_notify_flag = true
            print("Voltage notification flag set to True")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notification flag set to True"}')
            end
        elseif data.command == "notify_off" then
            -- Disable voltage notifications (Air720 compatible JSON response)
            vars.voltage_notify_flag = false
            print("Voltage notification flag set to False")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notification flag set to False"}')
            end
        elseif data.command == "volt_offset" then
            -- Set voltage offset (Air720 compatible JSON response)
            local offset = tonumber(data.value)
            if offset then
                vars.voltage_offset = offset
                print(string.format("Voltage offset set to %.2f", offset))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            else
                print("Invalid voltage offset value")
            end
        elseif data.command == "volt_threshold" then
            -- Set voltage threshold (Air720 compatible JSON response)
            local threshold = tonumber(data.value)
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                print(string.format("Voltage threshold set to %.2f", threshold))
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            else
                print("Invalid voltage threshold value")
            end
        elseif data.command:sub(1, 4) == "volt" then
            local offset = tonumber(data.command:sub(5))
            if offset then
                vars.voltage_offset = offset
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            end
        elseif data.command:sub(1, 2) == "th" then
            local threshold = tonumber(data.command:sub(3))
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            end
        elseif data.command and data.command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
            local phoneNumber, unitAmount = data.command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
            unitAmount = tonumber(unitAmount)
            if unitAmount and unitAmount <= 2000 and sms and sms.send then
                local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish(string.format('{"status":"Success","message":"Sent Unitel command for %s with %s units"}', phoneNumber, unitAmount))
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send Unitel command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"Invalid Unitel command"}')
            end
        elseif data.command == "gpiotest" then
            -- Simplified GPIO test
            sys.taskInit(function()
                local relays = {"Relay1", "Relay2", "Relay3", "KeyPower", "Key1", "Key2"}
                for _, relay in ipairs(relays) do
                    PinModule.relayControl(relay, 1)
                    sys.wait(1000)
                    PinModule.relayControl(relay, 0)
                    sys.wait(200)
                end
                playBeep("check")
            end)
        end
    else
        -- Handle Air720-style string commands (non-JSON)
        local command = payload:lower()
        print("String Command:", command)
        playBeep("check")

        if command == "check" then
            commands.checkCommand()
            publishSensorData()
        elseif command == "lock" then
            commands.lockCommand()
        elseif command == "unlock" then
            commands.unlockCommand()
        elseif command == "as" then
            commands.asCommand()
        elseif command == "untar" then
            commands.untarCommand()
        elseif command == "notify on" then
            -- Enable voltage notifications (Air720 format)
            vars.voltage_notify_flag = true
            print("Voltage notification flag set to True")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notifications enabled"}')
            end
        elseif command == "notify off" then
            -- Disable voltage notifications (Air720 format)
            vars.voltage_notify_flag = false
            print("Voltage notification flag set to False")
            if mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Voltage notifications disabled"}')
            end
        elseif command:sub(1, 4) == "volt" then
            local offset = tonumber(command:sub(5))
            if offset then
                vars.voltage_offset = offset
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage offset set to %.2f"}', offset))
                end
            end
        elseif command:sub(1, 2) == "th" then
            local threshold = tonumber(command:sub(3))
            if threshold and threshold > 0 then
                vars.voltage_threshold = threshold
                if mqtt_module.is_ready() then
                    mqtt_module.publish(string.format('{"status":"Voltage threshold set to %.2f"}', threshold))
                end
            end
        elseif command == "volt status" then
            -- Get voltage configuration status (Air720 format)
            local status = {
                current_voltage = readVoltage(),
                voltage_offset = vars.voltage_offset,
                voltage_threshold = vars.voltage_threshold,
                voltage_notify_flag = vars.voltage_notify_flag
            }
            print("Voltage Status:")
            print("  Current voltage: " .. string.format("%.2f", status.current_voltage) .. "V")
            print("  Voltage offset: " .. string.format("%.2f", status.voltage_offset))
            print("  Voltage threshold: " .. string.format("%.2f", status.voltage_threshold))
            print("  Notifications: " .. (status.voltage_notify_flag and "enabled" or "disabled"))
            if mqtt_module.is_ready() then
                local json_status = json.encode(status)
                mqtt_module.publish('{"voltage_status":' .. json_status .. '}')
            end
        elseif command == "geely atlas on" then
            -- Enable Geely Atlas mode (Air720 format)
            commands.enableGeelyMode()
            print("Geely Atlas mode enabled via MQTT")
        elseif command == "geely atlas off" then
            -- Disable Geely Atlas mode (Air720 format)
            commands.disableGeelyMode()
            print("Geely Atlas mode disabled via MQTT")
        elseif command:sub(1, 5) == "time:" then
            -- Air720 format: "time:param_name:value_ms"
            -- Example: "time:lock_press:1500"
            local parts = {}
            for part in string.gmatch(command, "[^:]+") do
                table.insert(parts, part)
            end

            if #parts == 3 then
                local param = parts[2]
                local value = tonumber(parts[3])
                if param and value and value > 0 then
                    local success = commands.setTimingParameter(param, value)
                    if success then
                        print(string.format("Timing parameter %s set to %d ms", param, value))
                    else
                        print("Failed to set timing parameter:", param)
                    end
                else
                    print("Invalid timing parameter or value")
                end
            else
                print("Invalid time command format. Use: time:param_name:value_ms")
            end
        elseif command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
            local phoneNumber, unitAmount = command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
            unitAmount = tonumber(unitAmount)
            if unitAmount and unitAmount <= 2000 and sms and sms.send then
                local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
                if mqtt_module.is_ready() then
                    if success then
                        mqtt_module.publish(string.format('{"status":"Success","message":"Sent Unitel command for %s with %s units"}', phoneNumber, unitAmount))
                    else
                        mqtt_module.publish('{"status":"Error","message":"Failed to send Unitel command"}')
                    end
                end
            elseif mqtt_module.is_ready() then
                mqtt_module.publish('{"status":"Error","message":"Invalid Unitel command"}')
            end
        else
            print("Unknown command:", command)
        end
    end
end

-- MQTT initial data publishing handler
local function handleInitialDataPublish()
    print("Publishing initial sensor data to MQTT")
    -- Call publishSensorData which will start a task
    if publishSensorData() then
        print("Started task to publish initial sensor data")
    else
        print("Failed to start task for publishing initial sensor data")
    end
end

-- Simplified SMS command processing with authorization
local function processSmsCommand(command, callback_number)
    -- Extract last 8 digits for authorization check
    local short_num = string.sub(callback_number, -8)

    -- Check if the number is authorized (compare using short numbers)
    local is_authorized = (short_num == vars.phone_number1 or
                          short_num == vars.phone_number2 or
                          short_num == vars.phone_number3)

    -- Commands that require authorization (Air720 compatible)
    local restricted_commands = {
        ["as"] = true,
        ["untar"] = true,
        ["lock"] = true,
        ["unlock"] = true
    }

    -- Check authorization for restricted commands
    if restricted_commands[command] and not is_authorized then
        sendSms(callback_number, "taniulaagvi dugaar!")  -- Unauthorized number message
        print("Unauthorized SMS command attempt from:", callback_number, "Command:", command)
        return
    end

    -- Check license for restricted commands (only if authorized)
    if not vars.isLicensed and restricted_commands[command] then
        sendSms(callback_number, "License expired")
        return
    end

    local commands_map = {
        check = function()
            local temp_hum = readSHTC3()
            local voltage = readVoltage()
            sendSms(callback_number, string.format("Batt: %.2fV | Temp: %.1fC", voltage, temp_hum.temperature))
        end,
        lock = function() lockCommand(); sendSms(callback_number, "locked") end,
        unlock = function() unlockCommand(); sendSms(callback_number, "unlocked") end,
        as = function() asCommand(); sendSms(callback_number, "started") end,
        untar = function() untarCommand(); sendSms(callback_number, "stopped") end,
        version = function() sendSms(callback_number, VERSION) end,
        ["notify on"] = function() vars.voltage_notify_flag = true; sendSms(callback_number, "Voltage notifications enabled") end,
        ["notify off"] = function() vars.voltage_notify_flag = false; sendSms(callback_number, "Voltage notifications disabled") end,
        ["volt status"] = function()
            local voltage = readVoltage()
            sendSms(callback_number, string.format("Volt: %.2fV | Offset: %.2f | Threshold: %.2f | Notify: %s",
                voltage, vars.voltage_offset, vars.voltage_threshold, vars.voltage_notify_flag and "ON" or "OFF"))
        end
    }

    if commands_map[command] then
        commands_map[command]()
    elseif command:sub(1, 4) == "volt" then
        -- Voltage commands require authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        local offset = tonumber(command:sub(5))
        if offset then
            vars.voltage_offset = offset
            sendSms(callback_number, string.format("Voltage offset set to %.2f", offset))
        end
    elseif command:sub(1, 2) == "th" then
        -- Threshold commands require authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        local threshold = tonumber(command:sub(3))
        if threshold and threshold > 0 then
            vars.voltage_threshold = threshold
            sendSms(callback_number, string.format("Voltage threshold set to %.2f", threshold))
        end
    elseif command:match("^unitel:%d%d%d%d%d%d%d%d %d+$") then
        -- Unitel command requires authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        -- Handle unitel command via SMS: "unitel:88889999 1000"
        local phoneNumber, unitAmount = command:match("^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
        unitAmount = tonumber(unitAmount)

        if unitAmount and unitAmount <= 2000 then
            -- Send the command to the unitel service (1444)
            local success = sms.send("1444", phoneNumber .. " " .. unitAmount)
            if success then
                sendSms(callback_number, string.format("Unitel: %s units sent to %s", unitAmount, phoneNumber))
            else
                sendSms(callback_number, "Failed to send Unitel command")
            end
        else
            sendSms(callback_number, "Invalid Unitel: Phone 8 digits, amount <= 2000")
        end
    elseif command == "notify on" or command == "notify off" then
        -- Notify commands require authorization
        if not is_authorized then
            sendSms(callback_number, "taniulaagvi dugaar!")
            return
        end
        -- These are handled in commands_map above, but adding explicit check for clarity
    end
end

-- Simplified initialization
local function initTask()
    print("Air780EG Enhanced v" .. VERSION)

    -- Initialize core modules
    PinModule.setupPins()
    initSHTC3()
    initADC()
    initGPS()
    initSMS()

    -- Create user directory and load configuration
    my_utils.createDirectory("/user_dir")
    my_utils.loadConfiguration()

    -- Initialize mobile network and start monitoring
    initMobile()

    mqtt_module.init()

    -- Basic subscriptions
    sys.subscribe("MQTT_MESSAGE_RECEIVED", handleMQTTMessage)
    sys.subscribe("MQTT_PUBLISH_INITIAL_DATA", function() publishSensorData() end)
    sys.subscribe("MQTT_CONNECTED", function()
        PinModule.setCloudLED(1)
        if not mqtt_connection_beep_played then
            mqtt_connection_beep_played = true
            playBeep("check")
        end
    end)
    sys.subscribe("MQTT_DISCONNECTED", function()
        PinModule.setCloudLED(0)
        mqtt_connection_beep_played = false
    end)

    -- Start essential tasks
    sys.taskInit(sensorTask)
    sys.taskInit(uartTask)
    sys.timerLoopStart(monitorVoltage, 10000)

    -- Start network monitoring task for NetLED
    sys.taskInit(function()
        while true do
            sys.wait(5000)  -- Check every 5 seconds

            if mobile and mobile.status then
                local status = mobile.status()
                local rssi = getRSSI() or 0

                -- Check if network is registered or has signal
                if status == "REGISTERED" or status == "REGISTERED_ROAMING" or
                   status == 1 or status == 2 or status == 5 then
                    PinModule.setNetLED(1)
                elseif rssi and rssi ~= 0 then
                    -- Has signal but not fully registered - still turn on LED
                    PinModule.setNetLED(1)
                else
                    -- No network or signal
                    PinModule.setNetLED(0)
                end
            else
                -- Mobile module not available
                PinModule.setNetLED(0)
            end
        end
    end)

    -- SMS processing task
    sys.taskInit(function()
        while true do
            if vars.sms_data then
                processSmsCommand(vars.sms_data, vars.callback_number)
                vars.sms_data = nil
                vars.callback_number = nil
            end
            sys.wait(1000)
        end
    end)



    print("Air780EG Enhanced v" .. VERSION .. " - Ready!")
    print("Auth: " .. (vars.phone_number1 or "none") .. ", " .. (vars.phone_number2 or "none") .. ", " .. (vars.phone_number3 or "none"))
    print("Geely: " .. (vars.geely_atlas_mode and "ON" or "OFF") ..
          " | Voltage: " .. (vars.voltage_notify_flag and "ON" or "OFF"))
end

-- Main application entry point
local function main()
    -- Start the initialization task in a coroutine
    sys.taskInit(initTask)

    -- Keep the system running
    sys.run()
end

-- Start the application
main()
