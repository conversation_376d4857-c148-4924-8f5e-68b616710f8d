-- my_utils.lua - Simplified utility functions for Air780EG
local my_utils = {}
local vars = require('variables')

function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end

    -- Try alternative path without /user_dir if original path failed
    if string.find(path, "/user_dir/") then
        local alt_path = string.gsub(path, "/user_dir/", "/")
        local alt_file = io.open(alt_path, "r")
        if alt_file then
            alt_file:close()
            return true
        end
    end

    return false
end

function my_utils.readFile(path)
    local file = io.open(path, "r")
    if file then
        local content = file:read("*a")
        file:close()
        return content
    end

    -- Try alternative path without /user_dir if original path failed
    if string.find(path, "/user_dir/") then
        local alt_path = string.gsub(path, "/user_dir/", "/")
        local alt_file = io.open(alt_path, "r")
        if alt_file then
            local content = alt_file:read("*a")
            alt_file:close()
            print("Read from alternative path:", alt_path)
            return content
        end
    end

    return nil
end

function my_utils.createDirectory(path)
    -- For Air780, try multiple approaches to ensure directory exists
    print("Creating directory:", path)

    -- Method 1: Try rtos.make_dir (used in Air720)
    if rtos and rtos.make_dir then
        local success, err = pcall(rtos.make_dir, path)
        if success then
            print("Directory created with rtos.make_dir:", path)
            return true
        else
            print("rtos.make_dir failed:", err)
        end
    end

    -- Method 2: Try lfs if available
    if lfs and lfs.mkdir then
        local success, err = pcall(lfs.mkdir, path)
        if success then
            print("Directory created with lfs.mkdir:", path)
            return true
        else
            print("lfs.mkdir failed:", err)
        end
    end

    -- Method 3: Try fs module if available (Air780 specific)
    if fs and fs.mkdir then
        local success, err = pcall(fs.mkdir, path)
        if success then
            print("Directory created with fs.mkdir:", path)
            return true
        else
            print("fs.mkdir failed:", err)
        end
    end

    -- Method 4: Try io.mkdir if available
    if io and io.mkdir then
        local success, err = pcall(io.mkdir, path)
        if success then
            print("Directory created with io.mkdir:", path)
            return true
        else
            print("io.mkdir failed:", err)
        end
    end

    -- Method 5: Try to create a test file to verify directory exists/can be created
    local test_file = io.open(path .. "/test.tmp", "w")
    if test_file then
        test_file:close()
        os.remove(path .. "/test.tmp")
        print("Directory verified with test file:", path)
        return true
    end

    print("All directory creation methods failed for:", path)
    return false
end

function my_utils.writeToFile(path, content)
    print("Attempting to write to file:", path, "Content:", tostring(content))

    -- Ensure directory exists
    local dir = string.match(path, "(.+)/[^/]+$")
    if dir then
        print("Ensuring directory exists:", dir)
        local dir_created = my_utils.createDirectory(dir)
        if not dir_created then
            print("Warning: Directory creation failed, but attempting file write anyway")
        end
    end

    -- Try multiple file writing approaches

    -- Method 1: Standard io.open approach
    local file = io.open(path, "w")
    if file then
        local success, err = pcall(file.write, file, tostring(content))
        file:close()

        if success then
            print("Successfully wrote to file using io.open:", path)
            return true
        else
            print("Failed to write content with io.open:", path, "Error:", err)
        end
    else
        print("Failed to open file for writing with io.open:", path)
    end

    -- Method 2: Try alternative approach for Air780 using fs module
    if fs and fs.write then
        local success, err = pcall(fs.write, path, tostring(content))
        if success then
            print("Successfully wrote to file using fs.write:", path)
            return true
        else
            print("fs.write also failed:", err)
        end
    end

    -- Method 3: Try alternative path without /user_dir if original path failed
    if string.find(path, "/user_dir/") then
        local alt_path = string.gsub(path, "/user_dir/", "/")
        print("Trying alternative path:", alt_path)

        local alt_file = io.open(alt_path, "w")
        if alt_file then
            local success, err = pcall(alt_file.write, alt_file, tostring(content))
            alt_file:close()

            if success then
                print("Successfully wrote to alternative path:", alt_path)
                return true
            else
                print("Failed to write to alternative path:", alt_path, "Error:", err)
            end
        end
    end

    print("All file writing methods failed for:", path)
    return false
end

function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Load configuration from files
function my_utils.loadConfiguration()
    -- Load phone numbers
    if my_utils.fileExists("/user_dir/phone1.txt") then
        vars.phone_number1 = my_utils.readFile("/user_dir/phone1.txt")
    end
    if my_utils.fileExists("/user_dir/phone2.txt") then
        vars.phone_number2 = my_utils.readFile("/user_dir/phone2.txt")
    end
    if my_utils.fileExists("/user_dir/phone3.txt") then
        vars.phone_number3 = my_utils.readFile("/user_dir/phone3.txt")
    end

    -- Load basic settings
    if my_utils.fileExists("/user_dir/volt_offset.txt") then
        vars.voltage_offset = tonumber(my_utils.readFile("/user_dir/volt_offset.txt")) or 0
    end
    if my_utils.fileExists("/user_dir/license.txt") then
        vars.isLicensed = my_utils.readFile("/user_dir/license.txt") == "true"
    else
        vars.isLicensed = true
    end
end

return my_utils
