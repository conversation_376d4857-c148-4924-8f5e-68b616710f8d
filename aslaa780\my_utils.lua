-- my_utils.lua - Simplified utility functions for Air780EG
local my_utils = {}
local vars = require('variables')

function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then file:close(); return true end
    return false
end

function my_utils.readFile(path)
    local file = io.open(path, "r")
    if not file then return nil end
    local content = file:read("*a")
    file:close()
    return content
end

function my_utils.createDirectory(path)
    -- Try to create directory using lfs if available
    if lfs and lfs.mkdir then
        local success, err = pcall(lfs.mkdir, path)
        return success
    end
    -- Fallback: try to create a test file in the directory
    local test_file = io.open(path .. "/test.tmp", "w")
    if test_file then
        test_file:close()
        os.remove(path .. "/test.tmp")
        return true
    end
    return false
end

function my_utils.writeToFile(path, content)
    -- Ensure directory exists
    local dir = string.match(path, "(.+)/[^/]+$")
    if dir then
        my_utils.createDirectory(dir)
    end

    local file = io.open(path, "w")
    if not file then
        print("Failed to open file for writing:", path)
        return false
    end
    file:write(tostring(content))
    file:close()
    print("Successfully wrote to file:", path)
    return true
end

function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Load configuration from files
function my_utils.loadConfiguration()
    -- Load phone numbers
    if my_utils.fileExists("/user_dir/phone1.txt") then
        vars.phone_number1 = my_utils.readFile("/user_dir/phone1.txt")
    end
    if my_utils.fileExists("/user_dir/phone2.txt") then
        vars.phone_number2 = my_utils.readFile("/user_dir/phone2.txt")
    end
    if my_utils.fileExists("/user_dir/phone3.txt") then
        vars.phone_number3 = my_utils.readFile("/user_dir/phone3.txt")
    end

    -- Load basic settings
    if my_utils.fileExists("/user_dir/volt_offset.txt") then
        vars.voltage_offset = tonumber(my_utils.readFile("/user_dir/volt_offset.txt")) or 0
    end
    if my_utils.fileExists("/user_dir/license.txt") then
        vars.isLicensed = my_utils.readFile("/user_dir/license.txt") == "true"
    else
        vars.isLicensed = true
    end
end

return my_utils
