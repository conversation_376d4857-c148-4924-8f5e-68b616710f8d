-- my_utils.lua - Simplified utility functions for Air780EG
local my_utils = {}
local vars = require('variables')

function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

function my_utils.readFile(path)
    local file = io.open(path, "r")
    if file then
        local content = file:read("*a")
        file:close()
        return content
    end
    return nil
end

function my_utils.createDirectory(path)
    -- For Air780EG, use the official io.mkdir method
    print("Creating directory:", path)

    -- Method 1: Use io.mkdir (Air780EG official method)
    if io and io.mkdir then
        local success, err = io.mkdir(path)
        if success then
            print("Directory created with io.mkdir:", path)
            return true
        else
            print("io.mkdir failed:", err or "unknown error")
        end
    end

    -- Method 2: Try rtos.make_dir as fallback
    if rtos and rtos.make_dir then
        local success, err = pcall(rtos.make_dir, path)
        if success then
            print("Directory created with rtos.make_dir:", path)
            return true
        else
            print("rtos.make_dir failed:", err)
        end
    end

    -- Method 3: Try to create a test file to verify directory exists/can be created
    local test_file = io.open(path .. "/test.tmp", "w")
    if test_file then
        test_file:close()
        os.remove(path .. "/test.tmp")
        print("Directory verified with test file:", path)
        return true
    end

    print("All directory creation methods failed for:", path)
    return false
end

function my_utils.writeToFile(path, content)
    print("Attempting to write to file:", path, "Content:", tostring(content))

    -- Ensure directory exists
    local dir = string.match(path, "(.+)/[^/]+$")
    if dir then
        print("Ensuring directory exists:", dir)
        local dir_created = my_utils.createDirectory(dir)
        if not dir_created then
            print("Warning: Directory creation failed, but attempting file write anyway")
        end
    end

    -- Use standard Air780EG io.open approach
    local file = io.open(path, "w")
    if file then
        file:write(tostring(content))
        file:close()
        print("Successfully wrote to file:", path)
        return true
    else
        print("Failed to open file for writing:", path)
        return false
    end
end

function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Load configuration from files
function my_utils.loadConfiguration()
    -- Load phone numbers from /data/ directory (Air780EG standard)
    if my_utils.fileExists("/data/phone1.txt") then
        vars.phone_number1 = my_utils.readFile("/data/phone1.txt")
    end
    if my_utils.fileExists("/data/phone2.txt") then
        vars.phone_number2 = my_utils.readFile("/data/phone2.txt")
    end
    if my_utils.fileExists("/data/phone3.txt") then
        vars.phone_number3 = my_utils.readFile("/data/phone3.txt")
    end

    -- Load basic settings
    if my_utils.fileExists("/data/volt_offset.txt") then
        vars.voltage_offset = tonumber(my_utils.readFile("/data/volt_offset.txt")) or 0
    end
    if my_utils.fileExists("/data/license.txt") then
        vars.isLicensed = my_utils.readFile("/data/license.txt") == "true"
    else
        vars.isLicensed = true
    end
end

return my_utils
