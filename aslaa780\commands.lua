-- commands.lua - Command functions for Air780EG
local commands = {}

-- Import required modules
local sys = require("sys")
local vars = require('variables')
local PinModule = require('pin_module')



-- Simple beep function for Air780
local function playBeep(sound_type)
    if vars.sound_flag then
        print("Playing beep:", sound_type)
        -- Use the PinModule beep function
        if PinModule.beep then
            if sound_type == "as" then
                PinModule.beep(2000, 300)  -- 2kHz for 300ms
            elseif sound_type == "lock" then
                PinModule.beep(1500, 200)  -- 1.5kHz for 200ms
            elseif sound_type == "unlock" then
                PinModule.beep(1000, 200)  -- 1kHz for 200ms
            elseif sound_type == "check" then
                PinModule.beep(1200, 150)  -- 1.2kHz for 150ms
            elseif sound_type == "untar" then
                PinModule.beep(800, 400)   -- 800Hz for 400ms
            else
                PinModule.beep(1000, 200)  -- Default beep
            end
        end
    end
end

function commands.checkCommand()
    playBeep("check")
end

function commands.lockCommand()
    if not vars.isLicensed then return end
    PinModule.relayControl("KeyPower", 0)
    sys.taskInit(function()
        playBeep("lock")
        sys.wait(vars.lock_wait_duration or 2000)
        PinModule.relayControl("Key1", 1)
        sys.wait(vars.lock_press_duration or 1000)
        PinModule.relayControl("Key1", 0)
        if not vars.key_state then PinModule.relayControl("KeyPower", 1) end
    end)
end

function commands.unlockCommand()
    if not vars.isLicensed then return end
    PinModule.relayControl("KeyPower", 0)
    sys.taskInit(function()
        playBeep("unlock")
        sys.wait(vars.unlock_wait_duration or 1000)
        PinModule.relayControl("Key2", 1)
        sys.wait(vars.unlock_press_duration or 1000)
        PinModule.relayControl("Key2", 0)
        if not vars.key_state then PinModule.relayControl("KeyPower", 1) end
    end)
end

function commands.asCommand()
    -- Initialize key for immobilizer recognition (Air780 uses KeyPower instead of pmd.ldoset)
    local success = pcall(PinModule.relayControl, "KeyPower", 0) -- Air780 equivalent
    if not success then
        print("Error in KeyPower control during asCommand")
        return
    end

    -- Check if the device is licensed
    if not vars.isLicensed then
        print("Device not licensed. Cannot execute asCommand")
        playBeep("untar") -- Play error sound
        return
    end

    -- Ensure all timing variables have default values if they're nil
    if not vars.lock_init_duration then vars.lock_init_duration = 2000 end
    if not vars.lock_press_duration then vars.lock_press_duration = 1000 end
    if not vars.lock_wait_duration then vars.lock_wait_duration = 2000 end
    if not vars.between_press_duration then vars.between_press_duration = 1000 end
    if not vars.remote_start_duration then vars.remote_start_duration = 4000 end
    if not vars.relay1_on_duration then vars.relay1_on_duration = 3000 end
    if not vars.relay2_on_duration then vars.relay2_on_duration = 3000 end
    if not vars.relay3_on_duration then vars.relay3_on_duration = 3000 end

    vars.carAlreadyStarted = true

    -- Move yielding part into a coroutine-safe context
    sys.taskInit(function()
        -- Check if we're in Geely Atlas mode
        if vars.geely_atlas_mode then
            print("Using Geely Atlas sequence for asCommand")
            sys.wait(vars.lock_init_duration)  -- wait some for key init
            print("Remote start button press")
            playBeep("as") -- Play sound 
            
            -- First lock sequence
            print("First lock press")
            success = pcall(PinModule.relayControl, "Key1", 1)
            if not success then
                print("Error in Key1 control (ON)")
                return
            end

            sys.wait(vars.lock_press_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Key1", 0)
            if not success then
                print("Error in Key1 control (OFF)")
                return
            end

            sys.wait(vars.between_press_duration)  -- Use configurable duration

            -- Second lock sequence
            print("Second lock press")
            success = pcall(PinModule.relayControl, "Key1", 1)
            if not success then
                print("Error in Key1 control (ON)")
                return
            end

            sys.wait(vars.lock_press_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Key1", 0)
            if not success then
                print("Error in Key1 control (OFF)")
                return
            end

            sys.wait(vars.between_press_duration)  -- Use configurable duration

            -- Remote start sequence using Relay2 (Air780 mapping)
            success = pcall(PinModule.relayControl, "Relay2", 1)
            if not success then
                print("Error in Relay2 control (ON)")
                return
            end

            vars.relay2_state = 1
            print("RELAY2 ON")

            sys.wait(vars.remote_start_duration)  -- Use configurable duration

            success = pcall(PinModule.relayControl, "Relay2", 0)
            if not success then
                print("Error in Relay2 control (OFF)")
                return
            end

            vars.relay2_state = 0
            print("RELAY2 OFF")

            vars.as_wait_s2_falling = true
        else
            -- Standard sequence for other vehicles (Air780 mapping: Relay1->Relay3, Relay2->Relay2)
            playBeep("as") -- Play sound
            sys.wait(vars.lock_press_duration)  -- Use configurable duration
            sys.wait(vars.lock_wait_duration) -- Wait for configurable duration before proceeding
            
            vars.relay1_state = 1  -- This will be Relay3 on Air780
            PinModule.relayControl("Relay3", 1) -- Air780: Relay1 becomes Relay3
            print("RELAY3 ON")
            sys.wait(vars.relay1_on_duration) -- Wait for configurable duration
            
            vars.relay2_state = 1
            PinModule.relayControl("Relay2", 1) -- Air780: Relay2 stays Relay2
            print("RELAY2 ON")
            sys.wait(vars.relay2_on_duration) -- Wait for configurable duration
            
            vars.relay2_state = 0
            PinModule.relayControl("Relay2", 0) -- Turn off Relay 2
            vars.relay1_state = 0
            PinModule.relayControl("Relay3", 0) -- Turn off Relay 3 (was Relay1)
            vars.as_wait_s2_falling = true

            if vars.key_state == false then
                PinModule.relayControl("KeyPower", 1) -- Air780: Turn off key power instead of pmd.ldoset
            end
        end

        -- Set up auto-shutdown timer if enabled
        if vars.auto_shutdown_enabled then
            -- Record the time of the 'as' command
            vars.last_as_command_time = os.time()

            -- Cancel any existing auto-shutdown timer
            if vars.auto_shutdown_timer_id then
                sys.timerStop(vars.auto_shutdown_timer_id)
                vars.auto_shutdown_timer_id = nil
            end

            -- Set up a new auto-shutdown timer with the configurable duration
            vars.auto_shutdown_timer_id = sys.timerStart(function()
                -- Use pcall to catch any errors in the auto-shutdown function
                local success, err = pcall(function()
                    print("Auto-shutdown check - Current time:", os.time())

                    -- For Air780, we'll use a simple time-based auto-shutdown
                    -- The voltage reading is handled in main.lua
                    local voltage = 13.6  -- Assume car is running for auto-shutdown

                    if voltage >= 13.5 then
                        print(string.format("Auto-shutdown triggered after %d minutes. Current voltage: %s",
                            vars.auto_shutdown_minutes, voltage))

                        -- Execute the untarCommand
                        local untar_success, untar_err = pcall(commands.untarCommand, 2000)
                        if not untar_success then
                            print("Failed to execute untar command: " .. (untar_err or "Unknown error"))
                        end


                    else
                        print("Auto-shutdown canceled - car appears to be off already. Voltage: " .. voltage)
                    end
                end)

                if not success then
                    print("Error in auto-shutdown timer: " .. (err or "Unknown error"))
                end

                -- Clear the timer ID
                vars.auto_shutdown_timer_id = nil
            end, vars.auto_shutdown_time)

            print(string.format("Auto-shutdown timer set for %d minutes", vars.auto_shutdown_minutes))
        end


    end)
end

function commands.untarCommand(duration)
    if not vars.isLicensed then playBeep("untar"); return end
    if vars.auto_shutdown_timer_id then
        sys.timerStop(vars.auto_shutdown_timer_id)
        vars.auto_shutdown_timer_id = nil
    end
    PinModule.relayControl("KeyPower", 0)

    if vars.geely_atlas_mode then
        sys.taskInit(function()
            sys.wait(2000); playBeep("untar")
            vars.relay2_state = 1; PinModule.relayControl("Relay2", 1); sys.wait(1000)
            vars.relay2_state = 0; PinModule.relayControl("Relay2", 0); sys.wait(1000)
            vars.relay2_state = 1; PinModule.relayControl("Relay2", 1); sys.wait(1000)
            vars.relay2_state = 0; PinModule.relayControl("Relay2", 0); sys.wait(2000)
            if not vars.key_state then PinModule.relayControl("KeyPower", 1) end
            vars.carAlreadyStarted = false
        end)
    else
        playBeep("untar"); vars.relay2_state = 1; PinModule.relayControl("Relay2", 1)
        sys.taskInit(function()
            sys.wait(duration or 2000)
            vars.relay2_state = 0; PinModule.relayControl("Relay2", 0)
            vars.carAlreadyStarted = false
        end)
    end
end

-- Geely mode functions
function commands.enableGeelyMode()
    vars.geely_atlas_mode = true
    print("Geely Atlas mode enabled")
end

function commands.disableGeelyMode()
    vars.geely_atlas_mode = false
    print("Geely Atlas mode disabled")
end

function commands.getGeelyModeStatus()
    return vars.geely_atlas_mode
end

-- Simplified timing functions
function commands.setTimingParameter(param_name, value_ms)
    local params = {
        lock_press = "lock_press_duration", unlock_press = "unlock_press_duration",
        lock_wait = "lock_wait_duration", unlock_wait = "unlock_wait_duration",
        between_press = "between_press_duration", remote_start = "remote_start_duration",
        relay1_on = "relay1_on_duration", relay2_on = "relay2_on_duration",
        relay3_on = "relay3_on_duration", lock_init = "lock_init_duration"
    }

    if params[param_name] then
        vars[params[param_name]] = value_ms
        print(param_name .. " set to " .. value_ms .. "ms")
        return true
    end
    return false
end

function commands.getTimingParameters()
    return {
        lock_press = vars.lock_press_duration, unlock_press = vars.unlock_press_duration,
        lock_wait = vars.lock_wait_duration, unlock_wait = vars.unlock_wait_duration,
        between_press = vars.between_press_duration, remote_start = vars.remote_start_duration,
        relay1_on = vars.relay1_on_duration, relay2_on = vars.relay2_on_duration,
        relay3_on = vars.relay3_on_duration, lock_init = vars.lock_init_duration
    }
end



-- Air720 compatible mirror command
function commands.mirrorCommand()
    print("Mirror command executed")

    -- Initialize key for immobilizer recognition (Air780 equivalent)
    if gpio then pcall(gpio.set, 10, 0) end  -- KeyPower equivalent to pmd.ldoset(7, 5)

    sys.taskInit(function()
        -- Check if we're in Geely Atlas mode
        if vars.geely_atlas_mode then
            print("Using Geely Atlas sequence for mirrorCommand")

            -- Alternate between unlock and lock commands (Air720 compatible)
            if not vars.last_mirror_action or vars.last_mirror_action == "unlock" or vars.last_mirror_action == "none" then
                -- Execute lock command
                print("Mirror executing lock command")
                PinModule.relayControl("Key1", 1) -- Lock button press
                sys.wait(vars.mirror_duration or 2000) -- Use configurable duration or default 2 seconds
                PinModule.relayControl("Key1", 0) -- Release lock button
                vars.last_mirror_action = "lock"
            else
                -- Execute unlock command
                print("Mirror executing unlock command")
                PinModule.relayControl("Key2", 1) -- Unlock button press
                sys.wait(8000) -- Fixed 8 seconds for unlock (Air720 compatible)
                PinModule.relayControl("Key2", 0) -- Release unlock button
                vars.last_mirror_action = "unlock"
            end

            print("Mirror function completed with action: " .. vars.last_mirror_action)
        else
            -- Original mirror command logic for other vehicles (Air720 compatible)
            PinModule.relayControl("Relay3", 1)
            print("RELAY3 ON")
            vars.relay3_state = 1

            -- Wait for fixed duration (Air720 compatible)
            sys.wait(3000) -- Fixed 3 seconds for non-Geely mode

            -- Turn off Relay3
            PinModule.relayControl("Relay3", 0)
            vars.relay3_state = 0
            print("RELAY3 OFF")
        end

        -- Power down if needed
        if not vars.key_state then
            if gpio then pcall(gpio.set, 10, 1) end  -- KeyPower off
        end
    end)
end

-- Air720 compatible test command for UART2
function commands.testCommand()
    print("Test command executed")

    -- Initialize key for immobilizer recognition (Air780 equivalent)
    if gpio then pcall(gpio.set, 10, 0) end  -- KeyPower equivalent to pmd.ldoset(7, 5)

    sys.taskInit(function()
        sys.wait(1000) -- Wait for 1 second before proceeding

        vars.relay1_state = 1
        PinModule.relayControl("Relay1", 1) -- Turn on Relay 1
        PinModule.relayControl("Relay3", 1) -- Turn on Relay 3
        print("RELAY1 ON, RELAY3 ON")

        sys.wait(2000) -- Wait for 2 seconds

        vars.relay2_state = 1
        PinModule.relayControl("Relay2", 1) -- Turn on Relay 2
        print("RELAY2 ON")

        sys.wait(2000) -- Wait for 2 seconds

        vars.relay2_state = 0
        PinModule.relayControl("Relay2", 0) -- Turn off Relay 2
        vars.relay1_state = 0
        PinModule.relayControl("Relay1", 0) -- Turn off Relay 1
        PinModule.relayControl("Relay3", 0) -- Turn off Relay 3
        print("RELAY1 OFF, RELAY2 OFF, RELAY3 OFF")

        -- Power down if needed
        if not vars.key_state then
            if gpio then pcall(gpio.set, 10, 1) end  -- KeyPower off
        end
    end)
end

return commands
